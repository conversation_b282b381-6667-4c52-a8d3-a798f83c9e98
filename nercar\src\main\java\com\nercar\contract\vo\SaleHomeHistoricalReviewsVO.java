package com.nercar.contract.vo;


import com.nercar.contract.enums.SteelNumerUnitEnum;
import lombok.Data;

import java.time.LocalDateTime;

@Data
public class SaleHomeHistoricalReviewsVO {
    private String id;
    private String code;
    private String statusName;
    private String specialRequirements;
    private String authorName; // 填表人
    private String salesmanName;
    private LocalDateTime createTime;
    private String technicalStandardName;
    private String customerName;
    private String customerPhone;
    private String steelTypeName;
    private String steelGradeName;
    private String standardName;
    private String specification;
    private String itemName;
    private Byte isHead;
    private String value1;
    private String value2;
    private String value3;
    private String[] norm;
    private Integer steelNumber;
    private SteelNumerUnitEnum steelNumberUnit;
    private String deliveryStatus;
    private String processingPurpose;
    private String submitTime;
    private String archiveTime;
    private String type;
    private String submitUser; // 发起人
}
