package com.nercar.contract.dto;

import lombok.Data;
import java.util.List;

@Data
public class SaleHomeHistoricalParam extends BasePageParam{
    private String customerName;
    private String steelGradeId;
    private String steelTypeId;
    private String startDate;
    private String endDate;
    private String code;
    private String contractInfoId;
    private List<String> contractInfoIds;  // 批量下载时使用的合同ID列表
    private String value4;  // 用于SQL查询中的#{value4}，对应contract_info_id

    // 新增查询字段
    private String standardName;      // 标准名称模糊查询
    private String specificationName; // 原始规格名称模糊查询
    private String submitUser;        // 发起人查询条件
}
